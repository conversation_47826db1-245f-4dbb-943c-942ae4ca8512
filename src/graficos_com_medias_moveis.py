#!/usr/bin/env python3
"""
Script para gerar gráficos das ações brasileiras com médias móveis de 50 e 200 dias
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

# Principais ações brasileiras
ACOES_PRINCIPAIS = [
    ('PETR4.SA', 'Petrobras PN'),
    ('PETR3.SA', 'Petrobras ON'),
    ('VALE3.SA', 'Vale ON'),
    ('ITUB4.SA', 'Itaú Unibanco PN'),
    ('BBDC4.SA', 'Bradesco PN'),
    ('ABEV3.SA', 'Ambev ON'),
    ('WEGE3.SA', 'WEG ON'),
    ('RENT3.SA', 'Localiza ON'),
    ('LREN3.SA', 'Lojas Renner ON'),
    ('MGLU3.SA', 'Magazine Luiza ON'),
    ('SUZB3.SA', 'Suzano ON'),
    ('JBSS3.SA', 'JBS ON'),
    ('B3SA3.SA', 'B3 ON'),
    ('RAIL3.SA', 'Rumo ON'),
    ('CCRO3.SA', 'CCR ON'),
    ('ELET3.SA', 'Eletrobras ON'),
    ('BBAS3.SA', 'Banco do Brasil ON'),
    ('RADL3.SA', 'Raia Drogasil ON'),
    ('CSAN3.SA', 'Cosan ON'),
    ('GGBR4.SA', 'Gerdau PN')
]

def obter_quantidade_carteira(ticker):
    """
    Obtém a quantidade total de ações de um ticker na carteira
    """
    try:
        csv_path = '../carteira.csv'
        df = pd.read_csv(csv_path)

        # Filtrar por ticker e somar quantidades
        ticker_data = df[df['ticker'] == ticker]
        if not ticker_data.empty:
            quantidade_total = ticker_data['quantidade'].sum()
            return quantidade_total
        else:
            return 0

    except Exception as e:
        print(f"❌ Erro ao obter quantidade da carteira para {ticker}: {e}")
        return 0

def obter_dados_com_medias(ticker, nome):
    """
    Obtém dados de 15 meses para ter dados suficientes para MM200
    """
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")
        
        stock = yf.Ticker(ticker)
        # Obter 15 meses para ter dados suficientes para MM200
        dados = stock.history(period="15mo")
        
        if dados.empty or len(dados) < 200:
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None
        
        # Calcular médias móveis
        dados['MM200'] = dados['Close'].rolling(window=200).mean()
        
        # Pegar apenas os últimos 12 meses para exibição
        dados_12m = dados.tail(252)  # ~252 dias úteis em 12 meses
        
        print(f"     ✅ {len(dados_12m)} dias (com MMs calculadas)")
        return dados_12m
        
    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def analisar_tendencia(dados):
    """
    Analisa a tendência baseada nas médias móveis
    """
    if dados is None or len(dados) < 50:
        return "Indefinida", "gray"

    preco_atual = dados['Close'].iloc[-1]
    mm200_atual = dados['MM200'].iloc[-1]

    # Verificar se as MMs estão válidas (não NaN)
    if pd.isna(mm200_atual):
        return "Indefinida", "gray"
    
    # Análise de tendência
    if preco_atual > mm200_atual:
        return "Altista", "darkgreen"
    elif preco_atual < mm200_atual:
        return "Baixista", "darkred"
    else:
        return "Lateral", "orange"

def criar_grafico_com_medias(ticker, nome, dados):
    """
    Cria gráfico com preço, médias móveis e volume
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # Calcular estatísticas
    preco_inicial = dados['Close'].iloc[0]
    preco_final = dados['Close'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100
    
    # Análise de tendência
    tendencia, cor_tendencia = analisar_tendencia(dados)
    
    # Gráfico 1: Preço + Médias Móveis
    ax1.plot(dados.index, dados['Close'], linewidth=2.5, color='#1f77b4', label='Preço de Fechamento', zorder=3)
    
    # Médias móveis
    ax1.plot(dados.index, dados['MM200'], linewidth=2, color='red', label='MM 200 dias', alpha=0.8, zorder=2)

    # Área entre preço e MM200 para visualizar posição relativa
    ax1.fill_between(dados.index, dados['Close'], dados['MM200'],
                     where=(dados['Close'] >= dados['MM200']),
                     color='lightgreen', alpha=0.3, interpolate=True, label='Preço > MM200')
    ax1.fill_between(dados.index, dados['Close'], dados['MM200'],
                     where=(dados['Close'] < dados['MM200']),
                     color='lightcoral', alpha=0.3, interpolate=True, label='Preço < MM200')
    
    # Título do gráfico - incluir quantidade de ações da carteira
    quantidade_carteira = obter_quantidade_carteira(ticker)
    if quantidade_carteira > 0:
        titulo = f'{nome} ({ticker.replace(".SA", "")}) - Preço + Médias Móveis (12 Meses) - {quantidade_carteira} ações na carteira'
    else:
        titulo = f'{nome} ({ticker.replace(".SA", "")}) - Preço + Médias Móveis (12 Meses)'

    ax1.set_title(titulo, fontsize=16, fontweight='bold')
    ax1.set_ylabel('Preço (R$)', fontsize=12)
    ax1.legend(loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # Estatísticas no gráfico
    mm200_atual = dados['MM200'].iloc[-1] if not pd.isna(dados['MM200'].iloc[-1]) else 0

    stats_text = f'Preço: R$ {preco_final:.2f} | Performance: {performance:+.1f}%\n'
    stats_text += f'MM200: R$ {mm200_atual:.2f}\n'
    stats_text += f'Tendência: {tendencia}'
    
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9, 
                      edgecolor=cor_tendencia, linewidth=2))
    
    # Gráfico 2: Volume
    ax2.bar(dados.index, dados['Volume']/1e6, alpha=0.7, color='purple', width=0.8)
    ax2.set_title('Volume de Negociação', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Data', fontsize=12)
    ax2.set_ylabel('Volume (Milhões)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('../results/figures/moving_averages', exist_ok=True)

    # Salvar gráfico
    nome_arquivo = f"../results/figures/moving_averages/mm_{ticker.replace('.SA', '')}_12m.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"     💾 {nome_arquivo}")
    
    return {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_atual': preco_final,
        'mm200': mm200_atual,
        'tendencia': tendencia,
        'cor_tendencia': cor_tendencia
    }

def criar_dashboard_medias_moveis(resultados):
    """
    Cria dashboard com análise das médias móveis
    """
    print("\n📊 Criando dashboard de médias móveis...")
    
    # Separar por tendência
    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r)
    
    # Criar gráfico de pizza das tendências
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # Gráfico 1: Distribuição de Tendências
    labels = list(tendencias.keys())
    sizes = [len(tendencias[label]) for label in labels]
    colors = ['darkgreen', 'green', 'orange', 'red', 'darkred', 'gray'][:len(labels)]
    
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Distribuição de Tendências', fontsize=14, fontweight='bold')
    
    # Gráfico 2: Performance vs Posição MM200
    performances = [r['performance'] for r in resultados]
    posicoes_mm200 = []
    cores_pontos = []

    for r in resultados:
        if r['preco_atual'] > r['mm200']:
            posicoes_mm200.append(1)  # Acima da MM200
            cores_pontos.append('green')
        else:
            posicoes_mm200.append(0)  # Abaixo da MM200
            cores_pontos.append('red')

    ax2.scatter(posicoes_mm200, performances, c=cores_pontos, alpha=0.7, s=100)
    ax2.set_xlabel('Posição relativa à MM200 (0=Abaixo, 1=Acima)')
    ax2.set_ylabel('Performance (%)')
    ax2.set_title('Performance vs Posição MM200', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    # Gráfico 3: Ranking de Performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)
    tickers = [r['ticker'].replace('.SA', '') for r in resultados_ord[:15]]  # Top 15
    performances_top = [r['performance'] for r in resultados_ord[:15]]
    cores_bars = [r['cor_tendencia'] for r in resultados_ord[:15]]
    
    bars = ax3.bar(range(len(tickers)), performances_top, color=cores_bars, alpha=0.7)
    ax3.set_title('TOP 15 - Performance com Tendência', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Ações')
    ax3.set_ylabel('Performance (%)')
    ax3.set_xticks(range(len(tickers)))
    ax3.set_xticklabels(tickers, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3, axis='y')
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.8)
    
    # Gráfico 4: Distância do Preço vs MM200
    distancias_preco = []
    nomes_acoes = []

    for r in resultados:
        if r['mm200'] > 0:
            distancia = ((r['preco_atual'] / r['mm200']) - 1) * 100
            distancias_preco.append(distancia)
            nomes_acoes.append(r['ticker'].replace('.SA', ''))

    # Ordenar por distância
    dados_ordenados = sorted(zip(nomes_acoes, distancias_preco), key=lambda x: x[1], reverse=True)
    nomes_ord = [x[0] for x in dados_ordenados[:15]]
    dist_ord = [x[1] for x in dados_ordenados[:15]]

    cores_dist = ['green' if d > 0 else 'red' for d in dist_ord]

    ax4.bar(range(len(nomes_ord)), dist_ord, color=cores_dist, alpha=0.7)
    ax4.set_title('TOP 15 - Distância Preço vs MM200', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Ações')
    ax4.set_ylabel('Preço vs MM200 (%)')
    ax4.set_xticks(range(len(nomes_ord)))
    ax4.set_xticklabels(nomes_ord, rotation=45, ha='right')
    ax4.grid(True, alpha=0.3, axis='y')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.8)
    
    plt.suptitle('Dashboard - Análise de Médias Móveis', fontsize=18, fontweight='bold')
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('../results/figures/dashboards', exist_ok=True)

    plt.savefig('../results/figures/dashboards/dashboard_medias_moveis.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("   💾 ../results/figures/dashboards/dashboard_medias_moveis.png")

def gerar_relatorio_medias_moveis(resultados):
    """
    Gera relatório detalhado das médias móveis
    """
    print("\n" + "="*100)
    print("📈 RELATÓRIO ANÁLISE TÉCNICA - MÉDIAS MÓVEIS (200 dias)")
    print("="*100)
    
    # Ordenar por performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)
    
    print(f"{'#':<3} {'Ticker':<8} {'Nome':<20} {'Perf%':<8} {'Preço':<8} {'MM200':<8} {'Tendência':<15}")
    print("-" * 90)

    for i, r in enumerate(resultados_ord, 1):
        emoji = "🟢" if r['performance'] >= 0 else "🔴"
        print(f"{i:<3} {emoji} {r['ticker'].replace('.SA', ''):<6} "
              f"{r['nome'][:19]:<20} {r['performance']:>+6.1f}% "
              f"{r['preco_atual']:>6.2f} {r['mm200']:>6.2f} "
              f"{r['tendencia']:<15}")
    
    # Estatísticas por tendência
    print("\n" + "="*100)
    print("📊 ESTATÍSTICAS POR TENDÊNCIA")
    print("="*100)
    
    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r['performance'])
    
    for tendencia, performances in tendencias.items():
        count = len(performances)
        media = np.mean(performances)
        print(f"{tendencia:<20}: {count:2d} ações | Performance média: {media:+6.1f}%")
    
    # Análise de cruzamentos
    print("\n" + "="*100)
    print("🎯 ANÁLISE DE POSICIONAMENTO")
    print("="*100)
    
    acima_mm200 = len([r for r in resultados if r['preco_atual'] > r['mm200']])

    print(f"Ações com preço acima da MM200: {acima_mm200:2d}/{len(resultados)} ({acima_mm200/len(resultados)*100:.1f}%)")

def main():
    print("🚀 GRÁFICOS COM MÉDIAS MÓVEIS - AÇÕES BRASILEIRAS")
    print("="*60)
    print("📊 Análise Técnica com:")
    print("   • Preço de fechamento")
    print("   • Média Móvel de 200 dias (MM200)")
    print("   • Análise de tendência")
    print("   • Volume de negociação")
    
    print(f"\n📋 Serão analisadas {len(ACOES_PRINCIPAIS)} ações principais")
    print("⏱️  Tempo estimado: 3-5 minutos")
    
    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return
    
    print(f"\n📈 Iniciando análise com médias móveis...")
    
    resultados = []
    sucesso = 0
    erro = 0
    
    for i, (ticker, nome) in enumerate(ACOES_PRINCIPAIS, 1):
        print(f"\n[{i:2d}/{len(ACOES_PRINCIPAIS)}]", end=" ")
        
        dados = obter_dados_com_medias(ticker, nome)
        
        if dados is not None:
            resultado = criar_grafico_com_medias(ticker, nome, dados)
            resultados.append(resultado)
            sucesso += 1
        else:
            erro += 1
    
    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")
    
    if resultados:
        # Criar dashboard
        criar_dashboard_medias_moveis(resultados)
        
        # Gerar relatório
        gerar_relatorio_medias_moveis(resultados)
        
        print(f"\n📁 ARQUIVOS GERADOS:")
        print(f"   • {sucesso} gráficos individuais com médias móveis")
        print("   • 1 dashboard de análise técnica")
        print("\n🎯 Todos os gráficos incluem:")
        print("   • Linha do preço de fechamento")
        print("   • Linha da MM200 (vermelha)")
        print("   • Área colorida (verde=preço>MM200, vermelho=preço<MM200)")
        print("   • Análise de tendência automática")
        
    else:
        print("\n❌ Nenhum gráfico foi gerado!")

if __name__ == "__main__":
    main()
