#!/usr/bin/env python3
"""
Script para análise de carteira de ações
Lê dados de um arquivo CSV e gera análises de rendimento individual e do conjunto
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import yfinance as yf
from datetime import datetime, timedelta
import os

# Configuração de estilo
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def carregar_carteira(arquivo_csv):
    """Carrega dados da carteira do arquivo CSV"""
    try:
        carteira = pd.read_csv(arquivo_csv)
        carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
        return carteira
    except Exception as e:
        print(f"Erro ao carregar carteira: {e}")
        return None

def obter_dados_historicos(tickers, data_inicio):
    """Obtém dados históricos das ações"""
    print(f"Obtendo dados históricos a partir de {data_inicio.strftime('%d/%m/%Y')}...")
    dados = {}

    for ticker in tickers:
        try:
            stock = yf.Ticker(ticker)
            # Obter dados recentes (últimos 30 dias para garantir que temos dados suficientes)
            hist = stock.history(period='1mo')
            if not hist.empty:
                # Filtrar apenas dados a partir da data de início
                hist_filtrado = hist[hist.index.date >= data_inicio.date()]
                if not hist_filtrado.empty:
                    dados[ticker] = hist_filtrado
                    print(f"✓ Dados obtidos para {ticker}: {len(hist_filtrado)} dias desde {data_inicio.strftime('%d/%m/%Y')}")
                else:
                    print(f"✗ Nenhum dado encontrado para {ticker} desde {data_inicio.strftime('%d/%m/%Y')}")
            else:
                print(f"✗ Nenhum dado encontrado para {ticker}")
        except Exception as e:
            print(f"✗ Erro ao obter dados para {ticker}: {e}")

    return dados

def consolidar_posicoes_por_ticker(carteira):
    """Consolida posições do mesmo ticker em uma única entrada
    Valores negativos na quantidade indicam vendas"""
    posicoes_consolidadas = {}

    for _, posicao in carteira.iterrows():
        ticker = posicao['ticker']
        quantidade = posicao['quantidade']
        data_compra = posicao['data_compra']
        preco_compra = posicao.get('preco_compra', 0)

        # Determinar se é compra ou venda
        eh_venda = quantidade < 0
        valor_transacao = abs(quantidade) * preco_compra  # Sempre positivo para cálculos

        if ticker not in posicoes_consolidadas:
            # Primeira ocorrência do ticker
            if eh_venda:
                # Não pode vender sem ter comprado antes - tratar como erro ou ignorar
                print(f"⚠️  Aviso: Tentativa de venda de {ticker} sem posição prévia. Ignorando transação.")
                continue

            posicoes_consolidadas[ticker] = {
                'ticker': ticker,
                'quantidade_total': quantidade,
                'valor_investido_total': valor_transacao,
                'valor_vendido_total': 0.0,
                'data_primeira_compra': data_compra,
                'transacoes': [{
                    'quantidade': quantidade,
                    'data_compra': data_compra,
                    'preco_compra': preco_compra,
                    'valor_transacao': valor_transacao,
                    'tipo': 'COMPRA'
                }]
            }
        else:
            # Ticker já existe, consolidar
            posicoes_consolidadas[ticker]['quantidade_total'] += quantidade

            if eh_venda:
                # É uma venda - subtrair do valor investido e adicionar ao valor vendido
                posicoes_consolidadas[ticker]['valor_investido_total'] -= valor_transacao
                posicoes_consolidadas[ticker]['valor_vendido_total'] += valor_transacao
                tipo_transacao = 'VENDA'
            else:
                # É uma compra - adicionar ao valor investido
                posicoes_consolidadas[ticker]['valor_investido_total'] += valor_transacao
                tipo_transacao = 'COMPRA'

                # Manter a data da primeira compra apenas para compras
                if data_compra < posicoes_consolidadas[ticker]['data_primeira_compra']:
                    posicoes_consolidadas[ticker]['data_primeira_compra'] = data_compra

            # Adicionar esta transação à lista
            posicoes_consolidadas[ticker]['transacoes'].append({
                'quantidade': quantidade,
                'data_compra': data_compra,
                'preco_compra': preco_compra,
                'valor_transacao': valor_transacao,
                'tipo': tipo_transacao
            })

    # Calcular preço médio de compra para cada ticker
    # Só calcular se ainda há posição (quantidade_total > 0)
    for ticker, dados in posicoes_consolidadas.items():
        if dados['quantidade_total'] > 0:
            # Calcular preço médio baseado apenas nas compras
            total_comprado = sum(t['valor_transacao'] for t in dados['transacoes'] if t['tipo'] == 'COMPRA')
            qtd_comprada = sum(t['quantidade'] for t in dados['transacoes'] if t['tipo'] == 'COMPRA')
            if qtd_comprada > 0:
                dados['preco_medio_compra'] = total_comprado / qtd_comprada
            else:
                dados['preco_medio_compra'] = 0
        else:
            # Posição zerada
            dados['preco_medio_compra'] = 0

    return posicoes_consolidadas

def calcular_rendimento_individual(carteira, dados_historicos):
    """Calcula rendimento individual de cada ação (consolidando posições do mesmo ticker)"""
    # Primeiro, consolidar posições por ticker
    posicoes_consolidadas = consolidar_posicoes_por_ticker(carteira)

    resultados = []

    for ticker, dados_consolidados in posicoes_consolidadas.items():
        if ticker in dados_historicos:
            hist = dados_historicos[ticker]

            quantidade_total = dados_consolidados['quantidade_total']
            preco_medio_compra = dados_consolidados['preco_medio_compra']
            data_primeira_compra = dados_consolidados['data_primeira_compra']
            valor_vendido_total = dados_consolidados['valor_vendido_total']

            preco_atual = hist['Close'].iloc[-1]

            # Cálculos consolidados
            # Valor atual baseado na quantidade líquida (pode ser zero se vendeu tudo)
            valor_atual = quantidade_total * preco_atual if quantidade_total > 0 else 0

            # Calcular valor total originalmente investido (todas as compras)
            valor_investido_original = sum(t['valor_transacao'] for t in dados_consolidados['transacoes'] if t['tipo'] == 'COMPRA')

            # Rendimento = (valor atual + valor já vendido) - valor investido original
            rendimento_absoluto = (valor_atual + valor_vendido_total) - valor_investido_original
            rendimento_percentual = (rendimento_absoluto / valor_investido_original) * 100 if valor_investido_original > 0 else 0

            # Dados históricos desde a primeira compra
            dados_desde_compra = hist[hist.index.date >= data_primeira_compra.date()].copy()
            if dados_desde_compra.empty:
                # Se não há dados desde a compra, usar os dados disponíveis
                dados_desde_compra = hist.copy()

            # Calcular valor da posição e valor investido variável ao longo do tempo
            dados_desde_compra['Quantidade_Acumulada'] = 0
            dados_desde_compra['Valor_Investido_Acumulado'] = 0.0
            dados_desde_compra['Valor_Investido_Bruto'] = 0.0  # Novo: sem subtração das vendas
            dados_desde_compra['Valor_Vendido_Acumulado'] = 0.0  # Novo: valor das vendas acumulado

            # Para cada data, calcular quantas ações já foram compradas/vendidas até aquela data
            for i, data_atual in enumerate(dados_desde_compra.index):
                quantidade_ate_data = 0
                valor_investido_ate_data = 0.0
                valor_investido_bruto_ate_data = 0.0
                valor_vendido_ate_data = 0.0

                for transacao in dados_consolidados['transacoes']:
                    if transacao['data_compra'].date() <= data_atual.date():
                        quantidade_ate_data += transacao['quantidade']  # Já considera sinal (+ para compra, - para venda)
                        if transacao['tipo'] == 'COMPRA':
                            valor_investido_ate_data += transacao['valor_transacao']
                            valor_investido_bruto_ate_data += transacao['valor_transacao']
                        else:  # VENDA
                            # Subtrair o valor da venda do valor investido acumulado (líquido)
                            valor_investido_ate_data -= transacao['valor_transacao']
                            # Mas não subtrair do valor investido bruto
                            # Adicionar ao valor vendido acumulado
                            valor_vendido_ate_data += transacao['valor_transacao']

                dados_desde_compra.iloc[i, dados_desde_compra.columns.get_loc('Quantidade_Acumulada')] = quantidade_ate_data
                dados_desde_compra.iloc[i, dados_desde_compra.columns.get_loc('Valor_Investido_Acumulado')] = valor_investido_ate_data
                dados_desde_compra.iloc[i, dados_desde_compra.columns.get_loc('Valor_Investido_Bruto')] = valor_investido_bruto_ate_data
                dados_desde_compra.iloc[i, dados_desde_compra.columns.get_loc('Valor_Vendido_Acumulado')] = valor_vendido_ate_data

            # Calcular valor da posição baseado na quantidade acumulada
            dados_desde_compra['Valor_Posicao'] = dados_desde_compra['Close'] * dados_desde_compra['Quantidade_Acumulada']

            # Calcular valor total recuperável (valor atual da posição + valor das vendas)
            dados_desde_compra['Valor_Total_Recuperavel'] = dados_desde_compra['Valor_Posicao'] + dados_desde_compra['Valor_Vendido_Acumulado']

            resultado = {
                'ticker': ticker,
                'quantidade': quantidade_total,
                'data_compra': data_primeira_compra,
                'preco_compra': preco_medio_compra,
                'preco_atual': preco_atual,
                'valor_investido': valor_investido_original,
                'valor_atual': valor_atual,
                'rendimento_absoluto': rendimento_absoluto,
                'rendimento_percentual': rendimento_percentual,
                'dados_historicos': dados_desde_compra,
                'transacoes_detalhadas': dados_consolidados['transacoes']  # Manter detalhes das transações
            }

            resultados.append(resultado)

    return resultados

def calcular_rendimento_carteira(resultados):
    """Calcula rendimento total da carteira"""
    valor_total_investido = sum(r['valor_investido'] for r in resultados)
    valor_total_atual = sum(r['valor_atual'] for r in resultados)
    # Somar todos os rendimentos individuais (que já incluem vendas)
    rendimento_total_absoluto = sum(r['rendimento_absoluto'] for r in resultados)
    rendimento_total_percentual = (rendimento_total_absoluto / valor_total_investido) * 100 if valor_total_investido > 0 else 0

    # Criar série temporal da carteira
    todas_datas = set()
    for resultado in resultados:
        todas_datas.update(resultado['dados_historicos'].index.date)

    todas_datas = sorted(todas_datas)
    valor_carteira_diario = []
    valor_investido_acumulado = []
    valor_investido_bruto_acumulado = []  # Novo: valor investido bruto
    valor_total_recuperavel_acumulado = []  # Novo: valor total recuperável

    for data in todas_datas:
        valor_dia = 0
        investido_ate_data = 0
        investido_bruto_ate_data = 0
        valor_recuperavel_ate_data = 0

        for resultado in resultados:
            hist = resultado['dados_historicos']
            data_compra = resultado['data_compra'].date()

            # Só incluir a ação se já foi comprada nesta data
            if data >= data_compra:
                try:
                    # Encontrar a linha correspondente à data
                    hist_data = hist[hist.index.date <= data]
                    if not hist_data.empty:
                        linha_data = hist_data.iloc[-1]

                        # Usar valor da posição se disponível, senão calcular
                        if 'Valor_Posicao' in hist.columns:
                            valor_dia += linha_data['Valor_Posicao']
                        else:
                            valor_dia += linha_data['Close'] * resultado['quantidade']

                        # Usar valor investido acumulado se disponível, senão usar valor total
                        if 'Valor_Investido_Acumulado' in hist.columns:
                            investido_ate_data += linha_data['Valor_Investido_Acumulado']
                        else:
                            investido_ate_data += resultado['valor_investido']

                        # Calcular valor investido bruto (sem subtração das vendas)
                        if 'Valor_Investido_Bruto' in hist.columns:
                            investido_bruto_ate_data += linha_data['Valor_Investido_Bruto']
                        else:
                            investido_bruto_ate_data += resultado['valor_investido']

                        # Calcular valor total recuperável (posição atual + vendas)
                        if 'Valor_Total_Recuperavel' in hist.columns:
                            valor_recuperavel_ate_data += linha_data['Valor_Total_Recuperavel']
                        else:
                            valor_recuperavel_ate_data += linha_data['Close'] * resultado['quantidade']
                except:
                    continue

        valor_carteira_diario.append(valor_dia)
        valor_investido_acumulado.append(investido_ate_data)
        valor_investido_bruto_acumulado.append(investido_bruto_ate_data)
        valor_total_recuperavel_acumulado.append(valor_recuperavel_ate_data)

    carteira_historico = pd.DataFrame({
        'Data': todas_datas,
        'Valor_Total': valor_carteira_diario,
        'Valor_Investido_Acumulado': valor_investido_acumulado,
        'Valor_Investido_Bruto': valor_investido_bruto_acumulado,
        'Valor_Total_Recuperavel': valor_total_recuperavel_acumulado
    })
    carteira_historico['Data'] = pd.to_datetime(carteira_historico['Data'])
    carteira_historico.set_index('Data', inplace=True)

    return {
        'valor_total_investido': valor_total_investido,
        'valor_total_atual': valor_total_atual,
        'rendimento_total_absoluto': rendimento_total_absoluto,
        'rendimento_total_percentual': rendimento_total_percentual,
        'historico': carteira_historico
    }

def gerar_graficos(resultados, carteira_total):
    """Gera gráficos de análise"""
    os.makedirs('results/figures', exist_ok=True)

    # 1. Gráfico individual de cada ação (melhorado com mais informações)
    n_acoes = len(resultados)
    if n_acoes > 0:
        fig, axes = plt.subplots(n_acoes, 2, figsize=(16, 6*n_acoes))
        if n_acoes == 1:
            axes = axes.reshape(1, -1)

        for i, resultado in enumerate(resultados):
            ticker = resultado['ticker']
            ticker_clean = ticker.replace('.SA', '')
            hist = resultado['dados_historicos']

            # Calcular estatísticas
            performance = resultado['rendimento_percentual']
            preco_max = hist['Close'].max()
            preco_min = hist['Close'].min()

            # Gráfico de preço com área de preenchimento
            axes[i, 0].plot(hist.index, hist['Close'], label=f'{ticker_clean} - Preço',
                           linewidth=2.5, color='#1f77b4')
            axes[i, 0].fill_between(hist.index, hist['Low'], hist['High'],
                                   alpha=0.2, color='#ff7f0e', label='Faixa Diária (Min-Max)')
            axes[i, 0].axhline(y=resultado['preco_compra'], color='red', linestyle='--',
                              linewidth=2, label=f'Preço Compra: R$ {resultado["preco_compra"]:.2f}')

            # Título com performance
            cor_performance = 'green' if performance >= 0 else 'red'
            axes[i, 0].set_title(f'{ticker_clean} - Evolução do Preço (Performance: {performance:+.2f}%)',
                                fontsize=14, fontweight='bold', color=cor_performance)
            axes[i, 0].set_ylabel('Preço (R$)', fontsize=12)

            # Adicionar estatísticas no gráfico
            stats_text = f'Máximo: R$ {preco_max:.2f}\nMínimo: R$ {preco_min:.2f}\nAtual: R$ {resultado["preco_atual"]:.2f}'
            cor_box = 'lightgreen' if performance >= 0 else 'lightcoral'
            axes[i, 0].text(0.02, 0.98, stats_text, transform=axes[i, 0].transAxes,
                           fontsize=10, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor=cor_box, alpha=0.8))

            # Formatar eixo X - ajustar baseado no número de dias
            num_dias = len(hist)
            if num_dias == 1:
                # Para um único dia, usar formatação simples
                axes[i, 0].set_xticks([hist.index[0]])
                axes[i, 0].set_xticklabels([hist.index[0].strftime('%d/%m/%Y')])
            elif num_dias <= 7:
                axes[i, 0].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[i, 0].xaxis.set_major_locator(mdates.DayLocator(interval=1))
                plt.setp(axes[i, 0].xaxis.get_majorticklabels(), rotation=45)
            else:
                axes[i, 0].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[i, 0].xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias//7)))
                plt.setp(axes[i, 0].xaxis.get_majorticklabels(), rotation=45)
            axes[i, 0].legend(loc='upper right')
            axes[i, 0].grid(True, alpha=0.3)

            # Gráfico de valor da posição com rendimento
            axes[i, 1].plot(hist.index, hist['Valor_Posicao'], label=f'{ticker_clean} - Valor Posição',
                           color='green', linewidth=2.5)

            # Verificar se há valor investido acumulado (para múltiplas compras)
            if 'Valor_Investido_Acumulado' in hist.columns:
                # Linha variável do valor investido (para múltiplas compras)
                axes[i, 1].plot(hist.index, hist['Valor_Investido_Acumulado'], color='red', linestyle='--',
                               linewidth=2, label=f'Valor Investido (Acumulado)')

                # Área de lucro/prejuízo baseada no valor investido acumulado
                axes[i, 1].fill_between(hist.index, hist['Valor_Posicao'], hist['Valor_Investido_Acumulado'],
                                       where=(hist['Valor_Posicao'] >= hist['Valor_Investido_Acumulado']),
                                       color='green', alpha=0.3, label='Lucro')
                axes[i, 1].fill_between(hist.index, hist['Valor_Posicao'], hist['Valor_Investido_Acumulado'],
                                       where=(hist['Valor_Posicao'] < hist['Valor_Investido_Acumulado']),
                                       color='red', alpha=0.3, label='Prejuízo')
            else:
                # Linha constante do valor investido (compra única)
                axes[i, 1].axhline(y=resultado['valor_investido'], color='red', linestyle='--',
                                  linewidth=2, label=f'Valor Investido: R$ {resultado["valor_investido"]:.2f}')

                # Área de lucro/prejuízo baseada no valor investido constante
                axes[i, 1].fill_between(hist.index, hist['Valor_Posicao'], resultado['valor_investido'],
                                       where=(hist['Valor_Posicao'] >= resultado['valor_investido']),
                                       color='green', alpha=0.3, label='Lucro')
                axes[i, 1].fill_between(hist.index, hist['Valor_Posicao'], resultado['valor_investido'],
                                       where=(hist['Valor_Posicao'] < resultado['valor_investido']),
                                       color='red', alpha=0.3, label='Prejuízo')

            axes[i, 1].set_title(f'{ticker_clean} - Valor da Posição (R$ {resultado["rendimento_absoluto"]:+.2f})',
                                fontsize=14, fontweight='bold', color=cor_performance)
            axes[i, 1].set_ylabel('Valor (R$)', fontsize=12)

            # Formatar eixo X - usar a mesma lógica do primeiro gráfico
            if num_dias == 1:
                # Para um único dia, usar formatação simples
                axes[i, 1].set_xticks([hist.index[0]])
                axes[i, 1].set_xticklabels([hist.index[0].strftime('%d/%m/%Y')])
            elif num_dias <= 7:
                axes[i, 1].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[i, 1].xaxis.set_major_locator(mdates.DayLocator(interval=1))
                plt.setp(axes[i, 1].xaxis.get_majorticklabels(), rotation=45)
            else:
                axes[i, 1].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[i, 1].xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias//7)))
                plt.setp(axes[i, 1].xaxis.get_majorticklabels(), rotation=45)
            axes[i, 1].legend(loc='upper left')
            axes[i, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('results/figures/rendimento_individual.png', dpi=300, bbox_inches='tight')
        plt.close()

    # 2. Gráfico da carteira total com ações individuais
    if not carteira_total['historico'].empty:
        n_acoes = len(resultados)
        # Criar layout: 3 gráficos da carteira + n gráficos das ações individuais
        fig, axes = plt.subplots(3 + n_acoes, 1, figsize=(16, 10 + 4*n_acoes))

        # Gráfico 1: Valor atual do portfólio vs valor investido líquido
        axes[0].plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Total'],
                    label='Valor Atual do Portfólio', linewidth=3, color='#1f77b4')
        axes[0].plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Investido_Acumulado'],
                    color='red', linestyle='--', linewidth=2, label='Valor Investido (Líquido)')

        # Área de lucro/prejuízo
        axes[0].fill_between(carteira_total['historico'].index,
                            carteira_total['historico']['Valor_Total'],
                            carteira_total['historico']['Valor_Investido_Acumulado'],
                            where=(carteira_total['historico']['Valor_Total'] >= carteira_total['historico']['Valor_Investido_Acumulado']),
                            color='green', alpha=0.3, label='Lucro')
        axes[0].fill_between(carteira_total['historico'].index,
                            carteira_total['historico']['Valor_Total'],
                            carteira_total['historico']['Valor_Investido_Acumulado'],
                            where=(carteira_total['historico']['Valor_Total'] < carteira_total['historico']['Valor_Investido_Acumulado']),
                            color='red', alpha=0.3, label='Prejuízo')

        # Calcular performance baseada nas curvas mostradas (valores líquidos)
        valor_portfolio_atual = carteira_total['historico']['Valor_Total'].iloc[-1]
        valor_investido_liquido_atual = carteira_total['historico']['Valor_Investido_Acumulado'].iloc[-1]
        performance_liquida = ((valor_portfolio_atual / valor_investido_liquido_atual) - 1) * 100 if valor_investido_liquido_atual > 0 else 0

        cor_titulo = 'green' if performance_liquida >= 0 else 'red'
        axes[0].set_title(f'Evolução do Portfólio vs Valor Investido Líquido (Performance: {performance_liquida:+.2f}%)',
                         fontsize=14, fontweight='bold', color=cor_titulo)
        axes[0].set_ylabel('Valor (R$)', fontsize=12)

        # Formatar eixo X baseado no número de dias
        num_dias_carteira = len(carteira_total['historico'])
        if num_dias_carteira == 1:
            axes[0].set_xticks([carteira_total['historico'].index[0]])
            axes[0].set_xticklabels([carteira_total['historico'].index[0].strftime('%d/%m/%Y')])
        elif num_dias_carteira <= 7:
            axes[0].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            axes[0].xaxis.set_major_locator(mdates.DayLocator(interval=1))
            plt.setp(axes[0].xaxis.get_majorticklabels(), rotation=45)
        else:
            axes[0].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            axes[0].xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias_carteira//7)))
            plt.setp(axes[0].xaxis.get_majorticklabels(), rotation=45)
        axes[0].legend(loc='upper left')
        axes[0].grid(True, alpha=0.3)

        # Gráfico 2: Rendimento percentual acumulado
        # Calcular rendimento percentual baseado no valor investido acumulado de cada dia
        rendimento_pct = ((carteira_total['historico']['Valor_Total'] - carteira_total['historico']['Valor_Investido_Acumulado']) /
                         carteira_total['historico']['Valor_Investido_Acumulado']) * 100
        # Substituir valores infinitos ou NaN por 0 (quando não há investimento ainda)
        rendimento_pct = rendimento_pct.fillna(0).replace([np.inf, -np.inf], 0)
        axes[1].plot(carteira_total['historico'].index, rendimento_pct,
                    label='Rendimento %', linewidth=3, color='green' if performance_liquida >= 0 else 'red')
        axes[1].axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=1)
        axes[1].fill_between(carteira_total['historico'].index, rendimento_pct, 0,
                            where=(rendimento_pct >= 0), color='green', alpha=0.3)
        axes[1].fill_between(carteira_total['historico'].index, rendimento_pct, 0,
                            where=(rendimento_pct < 0), color='red', alpha=0.3)
        axes[1].set_title('Rendimento Percentual da Carteira', fontsize=14, fontweight='bold')
        axes[1].set_ylabel('Rendimento (%)', fontsize=12)

        # Usar a mesma formatação de data
        if num_dias_carteira == 1:
            axes[1].set_xticks([carteira_total['historico'].index[0]])
            axes[1].set_xticklabels([carteira_total['historico'].index[0].strftime('%d/%m/%Y')])
        elif num_dias_carteira <= 7:
            axes[1].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            axes[1].xaxis.set_major_locator(mdates.DayLocator(interval=1))
            plt.setp(axes[1].xaxis.get_majorticklabels(), rotation=45)
        else:
            axes[1].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            axes[1].xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias_carteira//7)))
            plt.setp(axes[1].xaxis.get_majorticklabels(), rotation=45)
        axes[1].legend(loc='upper left')
        axes[1].grid(True, alpha=0.3)

        # Gráfico 3: Valor Total Recuperável (posição atual + vendas)
        if 'Valor_Total_Recuperavel' in carteira_total['historico'].columns:
            axes[2].plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Total_Recuperavel'],
                        color='#4169E1', linewidth=2.5, label='Valor Total Recuperável')
            axes[2].plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Investido_Bruto'],
                        color='#2E8B57', linewidth=2, linestyle='--', alpha=0.7, label='Valor Investido (Bruto)')

            # Área de lucro/prejuízo comparando recuperável vs investido bruto
            axes[2].fill_between(carteira_total['historico'].index,
                                carteira_total['historico']['Valor_Total_Recuperavel'],
                                carteira_total['historico']['Valor_Investido_Bruto'],
                                where=(carteira_total['historico']['Valor_Total_Recuperavel'] >= carteira_total['historico']['Valor_Investido_Bruto']),
                                color='green', alpha=0.3, label='Lucro Total')
            axes[2].fill_between(carteira_total['historico'].index,
                                carteira_total['historico']['Valor_Total_Recuperavel'],
                                carteira_total['historico']['Valor_Investido_Bruto'],
                                where=(carteira_total['historico']['Valor_Total_Recuperavel'] < carteira_total['historico']['Valor_Investido_Bruto']),
                                color='red', alpha=0.3, label='Prejuízo Total')

            # Calcular rendimento como (Valor do Portfólio + Vendas) / Investido Total
            valor_recuperavel_atual = carteira_total['historico']['Valor_Total_Recuperavel'].iloc[-1]
            valor_investido_total = carteira_total['historico']['Valor_Investido_Bruto'].iloc[-1]  # Valor investido original (sem subtração das vendas)
            print('11', valor_recuperavel_atual, valor_investido_total)
            rendimento_recuperavel_pct = ((valor_recuperavel_atual / valor_investido_total)-1) * 100 if valor_investido_total > 0 else 0

            cor_titulo_recuperavel = 'green' if rendimento_recuperavel_pct >= 0 else 'red'
            axes[2].set_title(f'Valor Total Recuperável - Valor Atual + Vendas (Rendimento: {rendimento_recuperavel_pct:+.2f}%)',
                             fontsize=14, fontweight='bold', color=cor_titulo_recuperavel)
            axes[2].set_ylabel('Valor (R$)', fontsize=12)
            axes[2].legend(loc='upper left')
            axes[2].grid(True, alpha=0.3)

            # Usar a mesma formatação de data
            if num_dias_carteira == 1:
                axes[2].set_xticks([carteira_total['historico'].index[0]])
                axes[2].set_xticklabels([carteira_total['historico'].index[0].strftime('%d/%m/%Y')])
            elif num_dias_carteira <= 7:
                axes[2].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[2].xaxis.set_major_locator(mdates.DayLocator(interval=1))
                plt.setp(axes[2].xaxis.get_majorticklabels(), rotation=45)
            else:
                axes[2].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[2].xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias_carteira//7)))
                plt.setp(axes[2].xaxis.get_majorticklabels(), rotation=45)

        # Gráficos 4+: Ações individuais (séries temporais)
        for i, resultado in enumerate(resultados):
            ax_idx = i + 3  # Começar no quarto gráfico
            ticker = resultado['ticker']
            ticker_clean = ticker.replace('.SA', '')
            hist = resultado['dados_historicos']
            performance = resultado['rendimento_percentual']

            # Gráfico de valor da posição
            axes[ax_idx].plot(hist.index, hist['Valor_Posicao'],
                             label=f'{ticker_clean} - Valor Posição',
                             color='green' if performance >= 0 else 'red', linewidth=2.5)

            # Verificar se há valor investido acumulado (para múltiplas compras)
            if 'Valor_Investido_Acumulado' in hist.columns:
                # Linha variável do valor investido (para múltiplas compras)
                axes[ax_idx].plot(hist.index, hist['Valor_Investido_Acumulado'], color='blue', linestyle='--',
                                 linewidth=2, label=f'Valor Investido (Acumulado)')

                # Área de lucro/prejuízo baseada no valor investido acumulado
                axes[ax_idx].fill_between(hist.index, hist['Valor_Posicao'], hist['Valor_Investido_Acumulado'],
                                         where=(hist['Valor_Posicao'] >= hist['Valor_Investido_Acumulado']),
                                         color='green', alpha=0.3, label='Lucro')
                axes[ax_idx].fill_between(hist.index, hist['Valor_Posicao'], hist['Valor_Investido_Acumulado'],
                                         where=(hist['Valor_Posicao'] < hist['Valor_Investido_Acumulado']),
                                         color='red', alpha=0.3, label='Prejuízo')
            else:
                # Linha constante do valor investido (compra única)
                axes[ax_idx].axhline(y=resultado['valor_investido'], color='blue', linestyle='--',
                                    linewidth=2, label=f'Valor Investido: R$ {resultado["valor_investido"]:.2f}')

                # Área de lucro/prejuízo baseada no valor investido constante
                axes[ax_idx].fill_between(hist.index, hist['Valor_Posicao'], resultado['valor_investido'],
                                         where=(hist['Valor_Posicao'] >= resultado['valor_investido']),
                                         color='green', alpha=0.3, label='Lucro')
                axes[ax_idx].fill_between(hist.index, hist['Valor_Posicao'], resultado['valor_investido'],
                                         where=(hist['Valor_Posicao'] < resultado['valor_investido']),
                                         color='red', alpha=0.3, label='Prejuízo')

            cor_performance = 'green' if performance >= 0 else 'red'
            axes[ax_idx].set_title(f'{ticker_clean} - Evolução da Posição (R$ {resultado["rendimento_absoluto"]:+.2f} | {performance:+.2f}%)',
                                  fontsize=12, fontweight='bold', color=cor_performance)
            axes[ax_idx].set_ylabel('Valor (R$)', fontsize=10)

            # Formatar eixo X
            num_dias = len(hist)
            if num_dias == 1:
                axes[ax_idx].set_xticks([hist.index[0]])
                axes[ax_idx].set_xticklabels([hist.index[0].strftime('%d/%m/%Y')])
            elif num_dias <= 7:
                axes[ax_idx].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[ax_idx].xaxis.set_major_locator(mdates.DayLocator(interval=1))
                plt.setp(axes[ax_idx].xaxis.get_majorticklabels(), rotation=45)
            else:
                axes[ax_idx].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[ax_idx].xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias//7)))
                plt.setp(axes[ax_idx].xaxis.get_majorticklabels(), rotation=45)

            axes[ax_idx].legend(loc='upper left', fontsize=9)
            axes[ax_idx].grid(True, alpha=0.3)

        # Adicionar xlabel apenas no último gráfico
        axes[-1].set_xlabel('Data', fontsize=12)

        plt.tight_layout()
        plt.savefig('results/figures/carteira_total.png', dpi=300, bbox_inches='tight')
        plt.close()

    # 3. Gráfico de barras: Comparação Valor Investido vs Valor Atual
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))

    categorias = ['Valor Total Investido', 'Valor Atual da Carteira']
    valores = [carteira_total['valor_total_investido'], carteira_total['valor_total_atual']]
    cores = ['#ff7f0e', '#1f77b4']  # Laranja para investido, azul para atual

    bars = ax.bar(categorias, valores, color=cores, alpha=0.8, edgecolor='black', linewidth=1)

    # Adicionar valores nas barras
    for bar, valor in zip(bars, valores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'R$ {valor:,.2f}', ha='center', va='bottom', fontweight='bold', fontsize=12)

    # Calcular diferença e adicionar no título
    diferenca = carteira_total['valor_total_atual'] - carteira_total['valor_total_investido']
    performance = carteira_total['rendimento_total_percentual']
    cor_titulo = 'green' if diferenca >= 0 else 'red'

    ax.set_title(f'Comparação: Investimento vs Valor Atual\n'
                f'Diferença: R$ {diferenca:+,.2f} ({performance:+.2f}%)',
                fontsize=14, fontweight='bold', color=cor_titulo)
    ax.set_ylabel('Valor (R$)', fontsize=12)
    ax.grid(True, alpha=0.3, axis='y')

    # Formatar eixo Y com separadores de milhares
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))

    plt.tight_layout()
    plt.savefig('results/figures/comparacao_investimento.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 4. Gráfico do Valor Investido Bruto (sem subtração das vendas)
    if not carteira_total['historico'].empty and 'Valor_Investido_Bruto' in carteira_total['historico'].columns:
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))

        ax.plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Investido_Bruto'],
                color='#2E8B57', linewidth=2.5, label='Valor Total Investido (Bruto)')

        ax.set_title('📈 Evolução do Valor Total Investido (Bruto)\nSem Subtração das Vendas',
                     fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Data', fontsize=12)
        ax.set_ylabel('Valor (R$)', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3, axis='y')

        # Formatar eixo Y com separadores de milhares
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))

        plt.tight_layout()
        plt.savefig('results/figures/valor_investido_bruto.png', dpi=300, bbox_inches='tight')
        plt.close()

    # 5. Gráfico do Valor Total Recuperável (posição atual + vendas)
    if not carteira_total['historico'].empty and 'Valor_Total_Recuperavel' in carteira_total['historico'].columns:
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))

        ax.plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Total_Recuperavel'],
                color='#4169E1', linewidth=2.5, label='Valor Total Recuperável')
        ax.plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Investido_Bruto'],
                color='#2E8B57', linewidth=2, linestyle='--', alpha=0.7, label='Valor Investido (Bruto)')

        ax.set_title('💰 Evolução do Valor Total Recuperável\nValor Atual da Carteira + Valor das Vendas',
                     fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Data', fontsize=12)
        ax.set_ylabel('Valor (R$)', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3, axis='y')

        # Formatar eixo Y com separadores de milhares
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))

        plt.tight_layout()
        plt.savefig('results/figures/valor_total_recuperavel.png', dpi=300, bbox_inches='tight')
        plt.close()

    print("📊 Gráficos salvos em 'results/figures/':")
    print("  - rendimento_individual.png (evolução de cada ação)")
    print("  - carteira_total.png (análise completa da carteira)")
    print("  - comparacao_investimento.png (comparação investido vs atual)")
    print("  - valor_investido_bruto.png (valor investido sem subtração das vendas)")
    print("  - valor_total_recuperavel.png (valor atual + vendas realizadas)")
    print("📈 Para visualizar os gráficos, abra os arquivos PNG na pasta results/figures/")

def gerar_relatorio(resultados, carteira_total):
    """Gera relatório resumo"""
    print("\n" + "="*70)
    print("📊 RELATÓRIO DE ANÁLISE DA CARTEIRA - SÉRIES TEMPORAIS")
    print("="*70)

    print(f"\n📅 DATA DA ANÁLISE: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
    print(f"📈 PERÍODO ANALISADO: Desde 26/06/2025 (início dos investimentos)")

    # Status da carteira
    performance_total = carteira_total['rendimento_total_percentual']
    if performance_total > 0:
        status = "📈 LUCRO"
        emoji_status = "🟢"
    elif performance_total < 0:
        status = "📉 PREJUÍZO"
        emoji_status = "🔴"
    else:
        status = "➡️ NEUTRO"
        emoji_status = "🟡"

    print(f"\n{emoji_status} STATUS GERAL: {status}")

    print(f"\n💰 RESUMO FINANCEIRO:")
    print(f"  Valor Total Investido: R$ {carteira_total['valor_total_investido']:.2f}")
    print(f"  Valor Atual da Carteira: R$ {carteira_total['valor_total_atual']:.2f}")
    print(f"  Rendimento Absoluto: R$ {carteira_total['rendimento_total_absoluto']:+.2f}")
    print(f"  Rendimento Percentual: {carteira_total['rendimento_total_percentual']:+.2f}%")

    # Calcular dias de investimento
    if not carteira_total['historico'].empty:
        dias_investimento = len(carteira_total['historico'])
        print(f"  Dias de Investimento: {dias_investimento}")
        if dias_investimento > 1:
            rendimento_diario_medio = carteira_total['rendimento_total_percentual'] / dias_investimento
            print(f"  Rendimento Médio Diário: {rendimento_diario_medio:+.3f}%")

    print(f"\n📋 DETALHAMENTO POR AÇÃO:")
    print("-" * 90)

    # Ordenar por rendimento percentual (melhor primeiro)
    resultados_ordenados = sorted(resultados, key=lambda x: x['rendimento_percentual'], reverse=True)

    for i, resultado in enumerate(resultados_ordenados, 1):
        ticker_clean = resultado['ticker'].replace('.SA', '')
        performance = resultado['rendimento_percentual']

        if performance > 0:
            emoji_acao = "📈🟢"
        elif performance < 0:
            emoji_acao = "📉🔴"
        else:
            emoji_acao = "➡️🟡"

        print(f"\n{emoji_acao} #{i} {ticker_clean}:")
        print(f"    Quantidade Total: {resultado['quantidade']} ações")
        print(f"    Data da Primeira Compra: {resultado['data_compra'].strftime('%d/%m/%Y')}")
        print(f"    Preço Médio de Compra: R$ {resultado['preco_compra']:.2f}")
        print(f"    Preço Atual: R$ {resultado['preco_atual']:.2f}")
        print(f"    Valor Total Investido: R$ {resultado['valor_investido']:.2f}")
        print(f"    Valor Atual: R$ {resultado['valor_atual']:.2f}")
        print(f"    Rendimento: R$ {resultado['rendimento_absoluto']:+.2f} ({resultado['rendimento_percentual']:+.2f}%)")

        # Participação na carteira
        participacao = (resultado['valor_atual'] / carteira_total['valor_total_atual']) * 100
        print(f"    Participação na Carteira: {participacao:.1f}%")

        # Variação de preço
        variacao_preco = ((resultado['preco_atual'] / resultado['preco_compra']) - 1) * 100
        print(f"    Variação do Preço: {variacao_preco:+.2f}%")

        # Se há múltiplas compras, mostrar detalhes
        if 'compras_detalhadas' in resultado and len(resultado['compras_detalhadas']) > 1:
            print(f"    📋 Detalhes das Compras ({len(resultado['compras_detalhadas'])} operações):")
            for j, compra in enumerate(resultado['compras_detalhadas'], 1):
                print(f"      {j}. {compra['data_compra'].strftime('%d/%m/%Y')}: "
                      f"{compra['quantidade']} ações × R$ {compra['preco_compra']:.2f} = "
                      f"R$ {compra['valor_investido']:.2f}")

def main():
    """Função principal"""
    arquivo_carteira = 'carteira.csv'

    print("📊 Iniciando análise da carteira com séries temporais...")

    # Carregar carteira
    carteira = carregar_carteira(arquivo_carteira)
    if carteira is None:
        return

    print(f"📈 Carteira carregada: {len(carteira)} posições")

    # Forçar data de início para 26/06/2025 conforme solicitado
    data_inicio = pd.to_datetime('2025-06-26')
    print(f"📅 Analisando séries temporais desde: {data_inicio.strftime('%d/%m/%Y')}")

    # Obter dados históricos - começar a partir de 26/06/2025
    tickers = carteira['ticker'].unique()
    dados_historicos = obter_dados_historicos(tickers, data_inicio)
    
    if not dados_historicos:
        print("Nenhum dado histórico foi obtido. Verifique os tickers.")
        return
    
    # Calcular rendimentos
    resultados = calcular_rendimento_individual(carteira, dados_historicos)
    carteira_total = calcular_rendimento_carteira(resultados)
    
    # Gerar gráficos
    gerar_graficos(resultados, carteira_total)
    
    # Gerar relatório
    gerar_relatorio(resultados, carteira_total)
    
    # Salvar resultados em CSV
    os.makedirs('results', exist_ok=True)
    df_resultados = pd.DataFrame([{
        'ticker': r['ticker'],
        'quantidade': r['quantidade'],
        'data_compra': r['data_compra'],
        'preco_compra': r['preco_compra'],
        'preco_atual': r['preco_atual'],
        'valor_investido': r['valor_investido'],
        'valor_atual': r['valor_atual'],
        'rendimento_absoluto': r['rendimento_absoluto'],
        'rendimento_percentual': r['rendimento_percentual']
    } for r in resultados])
    
    df_resultados.to_csv('results/analise_carteira.csv', index=False)
    print(f"\nResultados salvos em 'results/analise_carteira.csv'")

if __name__ == "__main__":
    main()
