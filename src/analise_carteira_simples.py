#!/usr/bin/env python3
"""
Script simplificado para análise de carteira - ideal para carteiras novas
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yfinance as yf
from datetime import datetime
import os

# Configuração de estilo
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def carregar_carteira(arquivo_csv):
    """Carrega dados da carteira do arquivo CSV"""
    try:
        carteira = pd.read_csv(arquivo_csv)
        carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
        return carteira
    except Exception as e:
        print(f"Erro ao carregar carteira: {e}")
        return None

def obter_precos_atuais(tickers):
    """Obtém preços atuais das ações"""
    print("Obtendo preços atuais...")
    precos = {}
    
    for ticker in tickers:
        try:
            stock = yf.Ticker(ticker)
            hist = stock.history(period='1d')
            if not hist.empty:
                precos[ticker] = hist['Close'].iloc[-1]
                print(f"✓ Preço obtido para {ticker}: R$ {precos[ticker]:.2f}")
            else:
                print(f"✗ Nenhum preço encontrado para {ticker}")
        except Exception as e:
            print(f"✗ Erro ao obter preço para {ticker}: {e}")
    
    return precos

def consolidar_posicoes_simples(carteira):
    """Consolida posições do mesmo ticker, tratando valores negativos como vendas"""
    posicoes_consolidadas = {}

    for _, posicao in carteira.iterrows():
        ticker = posicao['ticker']
        quantidade = posicao['quantidade']
        data_compra = posicao['data_compra']
        preco_compra = posicao['preco_compra']

        # Determinar se é compra ou venda
        eh_venda = quantidade < 0
        valor_transacao = abs(quantidade) * preco_compra

        if ticker not in posicoes_consolidadas:
            # Primeira ocorrência do ticker
            if eh_venda:
                print(f"⚠️  Aviso: Tentativa de venda de {ticker} sem posição prévia. Ignorando transação.")
                continue

            posicoes_consolidadas[ticker] = {
                'ticker': ticker,
                'quantidade_total': quantidade,
                'valor_investido_total': valor_transacao,
                'valor_investido_original': valor_transacao,  # Rastrear valor original
                'valor_vendido_total': 0.0,
                'data_primeira_compra': data_compra,
                'preco_medio_compra': preco_compra
            }
        else:
            # Ticker já existe, consolidar
            posicoes_consolidadas[ticker]['quantidade_total'] += quantidade

            if eh_venda:
                # É uma venda - subtrair do valor investido e adicionar ao valor vendido
                posicoes_consolidadas[ticker]['valor_investido_total'] -= valor_transacao
                posicoes_consolidadas[ticker]['valor_vendido_total'] += valor_transacao
            else:
                # É uma compra - adicionar ao valor investido e ao valor original
                posicoes_consolidadas[ticker]['valor_investido_total'] += valor_transacao
                posicoes_consolidadas[ticker]['valor_investido_original'] += valor_transacao
                # Atualizar data da primeira compra se necessário
                if data_compra < posicoes_consolidadas[ticker]['data_primeira_compra']:
                    posicoes_consolidadas[ticker]['data_primeira_compra'] = data_compra

            # Recalcular preço médio de compra baseado apenas nas compras
            if posicoes_consolidadas[ticker]['quantidade_total'] > 0:
                # Calcular preço médio baseado no valor investido total e quantidade atual
                total_compras = posicoes_consolidadas[ticker]['valor_investido_total']
                qtd_atual = posicoes_consolidadas[ticker]['quantidade_total']
                posicoes_consolidadas[ticker]['preco_medio_compra'] = total_compras / qtd_atual

    return posicoes_consolidadas

def calcular_rendimentos(carteira, precos_atuais):
    """Calcula rendimentos da carteira consolidando posições por ticker"""
    # Primeiro consolidar posições
    posicoes_consolidadas = consolidar_posicoes_simples(carteira)

    resultados = []

    for ticker, dados in posicoes_consolidadas.items():
        if ticker in precos_atuais:
            preco_atual = precos_atuais[ticker]

            quantidade_total = dados['quantidade_total']
            valor_investido_total = dados['valor_investido_total']
            valor_investido_original = dados['valor_investido_original']
            valor_vendido_total = dados['valor_vendido_total']
            preco_medio_compra = dados['preco_medio_compra']
            data_primeira_compra = dados['data_primeira_compra']

            # Cálculos
            valor_atual = quantidade_total * preco_atual if quantidade_total > 0 else 0
            # Rendimento = (valor atual + valor já vendido) - valor investido original
            rendimento_absoluto = (valor_atual + valor_vendido_total) - valor_investido_original
            rendimento_percentual = (rendimento_absoluto / valor_investido_original) * 100 if valor_investido_original > 0 else 0

            resultado = {
                'ticker': ticker,
                'quantidade': quantidade_total,
                'data_compra': data_primeira_compra,
                'preco_compra': preco_medio_compra,
                'preco_atual': preco_atual,
                'valor_investido': valor_investido_original,
                'valor_atual': valor_atual,
                'rendimento_absoluto': rendimento_absoluto,
                'rendimento_percentual': rendimento_percentual
            }

            resultados.append(resultado)

    return resultados

def gerar_graficos_simples(resultados):
    """Gera gráficos simples para carteira"""
    os.makedirs('results/figures', exist_ok=True)
    
    if not resultados:
        print("Nenhum resultado para gerar gráficos")
        return
    
    # Preparar dados
    tickers = [r['ticker'] for r in resultados]
    valores_investidos = [r['valor_investido'] for r in resultados]
    valores_atuais = [r['valor_atual'] for r in resultados]
    rendimentos_pct = [r['rendimento_percentual'] for r in resultados]
    
    # Calcular totais para o gráfico adicional
    valor_total_investido = sum(valores_investidos)
    valor_total_atual = sum(valores_atuais)

    # Criar figura com 4 subplots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Gráfico de barras - Valores Investidos vs Atuais
    x = np.arange(len(tickers))
    width = 0.35
    
    ax1.bar(x - width/2, valores_investidos, width, label='Valor Investido', alpha=0.8)
    ax1.bar(x + width/2, valores_atuais, width, label='Valor Atual', alpha=0.8)
    ax1.set_xlabel('Ações')
    ax1.set_ylabel('Valor (R$)')
    ax1.set_title('Comparação: Valor Investido vs Atual')
    ax1.set_xticks(x)
    ax1.set_xticklabels([t.replace('.SA', '') for t in tickers])
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Gráfico de pizza - Distribuição da carteira
    ax2.pie(valores_atuais, labels=[t.replace('.SA', '') for t in tickers], autopct='%1.1f%%', startangle=90)
    ax2.set_title('Distribuição da Carteira (Valor Atual)')
    
    # 3. Gráfico de barras - Rendimento percentual
    colors = ['green' if r >= 0 else 'red' for r in rendimentos_pct]
    ax3.bar(range(len(tickers)), rendimentos_pct, color=colors, alpha=0.7)
    ax3.set_xlabel('Ações')
    ax3.set_ylabel('Rendimento (%)')
    ax3.set_title('Rendimento por Ação')
    ax3.set_xticks(range(len(tickers)))
    ax3.set_xticklabels([t.replace('.SA', '') for t in tickers])
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax3.grid(True, alpha=0.3)
    
    # Adicionar valores nas barras do rendimento
    for i, v in enumerate(rendimentos_pct):
        ax3.text(i, v + (0.1 if v >= 0 else -0.1), f'{v:.2f}%',
                ha='center', va='bottom' if v >= 0 else 'top')

    # 4. Gráfico de barras - Total da Carteira
    categorias = ['Valor Investido', 'Valor Atual']
    valores_totais = [valor_total_investido, valor_total_atual]
    cores = ['lightblue', 'lightgreen' if valor_total_atual >= valor_total_investido else 'lightcoral']

    bars = ax4.bar(categorias, valores_totais, color=cores, alpha=0.8, edgecolor='black', linewidth=1)
    ax4.set_ylabel('Valor (R$)')
    ax4.set_title('Total da Carteira')
    ax4.grid(True, alpha=0.3, axis='y')

    # Adicionar valores nas barras
    for bar, valor in zip(bars, valores_totais):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + valor*0.01,
                f'R$ {valor:.2f}', ha='center', va='bottom', fontweight='bold')

    # Adicionar linha de diferença se houver lucro/prejuízo
    diferenca = valor_total_atual - valor_total_investido
    if abs(diferenca) > 0.01:  # Se diferença for significativa
        cor_diferenca = 'green' if diferenca > 0 else 'red'
        simbolo = '+' if diferenca > 0 else ''
        ax4.text(0.5, max(valores_totais) * 0.9,
                f'Diferença: {simbolo}R$ {diferenca:.2f}\n({simbolo}{((diferenca/valor_total_investido)*100):.2f}%)',
                ha='center', va='center', fontsize=12, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor=cor_diferenca, alpha=0.2))

    plt.tight_layout()
    plt.savefig('results/figures/analise_carteira_simples.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("Gráfico salvo em 'results/figures/analise_carteira_simples.png'")

def gerar_relatorio(resultados):
    """Gera relatório resumo"""
    if not resultados:
        print("Nenhum resultado para gerar relatório")
        return
    
    # Calcular totais
    valor_total_investido = sum(r['valor_investido'] for r in resultados)
    valor_total_atual = sum(r['valor_atual'] for r in resultados)
    rendimento_total_absoluto = valor_total_atual - valor_total_investido
    rendimento_total_percentual = (rendimento_total_absoluto / valor_total_investido) * 100 if valor_total_investido > 0 else 0
    
    print("\n" + "="*60)
    print("ANÁLISE SIMPLIFICADA DA CARTEIRA")
    print("="*60)
    
    print(f"\nDATA DA ANÁLISE: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
    
    print(f"\nRESUMO GERAL:")
    print(f"Valor Total Investido: R$ {valor_total_investido:.2f}")
    print(f"Valor Atual da Carteira: R$ {valor_total_atual:.2f}")
    print(f"Rendimento Absoluto: R$ {rendimento_total_absoluto:.2f}")
    print(f"Rendimento Percentual: {rendimento_total_percentual:.2f}%")
    
    # Status da carteira
    if rendimento_total_percentual > 0:
        status = "📈 LUCRO"
    elif rendimento_total_percentual < 0:
        status = "📉 PREJUÍZO"
    else:
        status = "➡️ NEUTRO"
    
    print(f"Status: {status}")
    
    print(f"\nDETALHE POR AÇÃO:")
    print("-" * 80)
    for resultado in resultados:
        ticker_clean = resultado['ticker'].replace('.SA', '')
        status_acao = "📈" if resultado['rendimento_percentual'] > 0 else "📉" if resultado['rendimento_percentual'] < 0 else "➡️"
        
        print(f"\n{status_acao} {ticker_clean}:")
        print(f"  Quantidade: {resultado['quantidade']} ações")
        print(f"  Data da Compra: {resultado['data_compra'].strftime('%d/%m/%Y')}")
        print(f"  Preço de Compra: R$ {resultado['preco_compra']:.2f}")
        print(f"  Preço Atual: R$ {resultado['preco_atual']:.2f}")
        print(f"  Valor Investido: R$ {resultado['valor_investido']:.2f}")
        print(f"  Valor Atual: R$ {resultado['valor_atual']:.2f}")
        print(f"  Rendimento: R$ {resultado['rendimento_absoluto']:.2f} ({resultado['rendimento_percentual']:.2f}%)")
        
        # Participação na carteira
        participacao = (resultado['valor_atual'] / valor_total_atual) * 100
        print(f"  Participação na Carteira: {participacao:.1f}%")

def main():
    """Função principal"""
    arquivo_carteira = 'carteira.csv'
    
    print("Iniciando análise simplificada da carteira...")
    
    # Carregar carteira
    carteira = carregar_carteira(arquivo_carteira)
    if carteira is None:
        return
    
    print(f"Carteira carregada: {len(carteira)} posições")
    
    # Obter preços atuais
    tickers = carteira['ticker'].unique()
    precos_atuais = obter_precos_atuais(tickers)
    
    if not precos_atuais:
        print("Nenhum preço atual foi obtido. Verifique os tickers.")
        return
    
    # Calcular rendimentos
    resultados = calcular_rendimentos(carteira, precos_atuais)
    
    if not resultados:
        print("Nenhum resultado calculado.")
        return
    
    # Gerar gráficos
    gerar_graficos_simples(resultados)
    
    # Gerar relatório
    gerar_relatorio(resultados)
    
    # Salvar resultados em CSV
    os.makedirs('results', exist_ok=True)
    df_resultados = pd.DataFrame(resultados)
    df_resultados.to_csv('results/analise_carteira_simples.csv', index=False)
    print(f"\nResultados salvos em 'results/analise_carteira_simples.csv'")

if __name__ == "__main__":
    main()
